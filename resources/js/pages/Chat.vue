<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue';
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { useStreamingChat } from '@/composables/useStreamingChat';
import { useClipboard } from '@vueuse/core';

import type { BreadcrumbItemType } from '@/types';

// Breadcrumbs for navigation
const breadcrumbs: BreadcrumbItemType[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Prism Chat', href: '/chat' },
];

// Chat functionality
const {
    messages,
    input,
    isLoading,
    error,
    status,
    hasMessages,
    sendMessage,
    clearMessages,
    loadUserContext,
    clearError,
    stop,
    setMessages,
} = useStreamingChat();

// UI state
const messagesContainer = ref<HTMLElement>();
const showWelcome = ref( true );
const copiedMessageId = ref<string | null>( null );

// Clipboard functionality
const { copy, copied, isSupported } = useClipboard();

// Auto-scroll to bottom when new messages arrive
const scrollToBottom = async () =>
{
    await nextTick();
    if ( messagesContainer.value )
    {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// Watch for new messages and scroll
watch( messages, (newMessages) => {
    console.log('Messages changed:', newMessages);
    scrollToBottom();
}, { deep: true } );
watch( isLoading, ( loading ) =>
{
    if ( loading )
    {
        scrollToBottom();
    }
} );

// Handle stopping generation
const handleStop = () =>
{
    stop();
};

// Handle clearing chat
const handleClearChat = () =>
{
    clearMessages();
    showWelcome.value = true;
    clearError();
};

// Handle error actions
const handleErrorDismiss = () =>
{
    clearError();
};

// Fallback clipboard copy using legacy method
const fallbackCopyToClipboard = ( text: string ): boolean =>
{
    try
    {
        // Create a temporary textarea element
        const textArea = document.createElement( 'textarea' );
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild( textArea );

        // Select and copy the text
        textArea.focus();
        textArea.select();
        const successful = document.execCommand( 'copy' );

        // Clean up
        document.body.removeChild( textArea );

        return successful;
    } catch ( err )
    {
        console.error( 'Fallback copy failed:', err );
        return false;
    }
};

// Handle copying to clipboard with fallback support
const copyToClipboard = async ( text: string, messageId: string ) =>
{
    let copySuccessful = false;
    let errorMessage = '';

    // Try modern clipboard API first
    if ( isSupported.value )
    {
        try
        {
            await copy( text );
            copySuccessful = true;
        } catch ( err )
        {
            console.error( 'Modern clipboard API failed:', err );
            errorMessage = 'Modern clipboard API failed';
        }
    }

    // Fallback to legacy method if modern API failed or isn't supported
    if ( !copySuccessful )
    {
        copySuccessful = fallbackCopyToClipboard( text );
        if ( !copySuccessful )
        {
            errorMessage = 'All clipboard methods failed';
        }
    }

    if ( copySuccessful )
    {
        // Show feedback for this specific message
        copiedMessageId.value = messageId;
        setTimeout( () =>
        {
            copiedMessageId.value = null;
        }, 2000 );
    } else
    {
        // Show a more user-friendly error with manual copy option
        const shouldShowText = confirm(
            `Unable to copy to clipboard automatically. ${ errorMessage }.\n\nWould you like to see the text so you can copy it manually?`
        );

        if ( shouldShowText )
        {
            // Show the text in a prompt for manual copying
            prompt( 'Copy this text manually:', text );
        }
    }
};

// Handle form submission (UChatPrompt handles Enter key automatically)
const handleSubmit = ( event?: Event ) =>
{
    if ( event )
    {
        event.preventDefault();
    }

    if ( !input.value.trim() || isLoading.value ) return;

    showWelcome.value = false;

    if ( !input.value.trim() || error.value || input.value.length > 4000 )
    {
        return;
    }

    sendMessage( input.value.trim() );
};

// Load user context on mount
onMounted( async () =>
{
    await loadUserContext();

    // Debug: Log messages structure
    console.log('Messages:', messages.value);
    console.log('Status:', status.value);

    // Add test messages to verify positioning
    setTimeout(() => {
        console.log('Adding test messages...');
        try {
            // Try to manually add messages for testing
            messages.value.push({
                id: 'test-user-1',
                role: 'user',
                content: 'This is a test user message',
                createdAt: new Date()
            } as any);

            messages.value.push({
                id: 'test-assistant-1',
                role: 'assistant',
                content: 'This is a test assistant response',
                createdAt: new Date()
            } as any);

            console.log('Test messages added:', messages.value);
        } catch (error) {
            console.error('Error adding test messages:', error);
        }
    }, 1000);
} );

// Welcome message examples
const examplePrompts = [
    "Help me create a prompt for content writing",
    "How can I improve this prompt for better results?",
    "What are the best practices for prompt engineering?",
    "Create a template for analyzing customer feedback",
];

const handleExampleClick = ( prompt: string ) =>
{
    input.value = prompt;
    handleSubmit();
};

const getStatus = ( status: string ) =>
{
    if ( status === 'streaming' || status === '' )
    {
        return 'streaming';
    }
    return 'ready';
};
</script>

<template>

    <Head title="Prism Chat" />

    <AppLayout :breadcrumbs=" breadcrumbs ">
        <div class="flex flex-col h-full min-h-0">
            <!-- Messages Container - Scrollable -->
            <div
                ref="messagesContainer"
                class="flex-1 overflow-y-auto min-h-0 pb-4"
            >
                <UChatMessages :messages=" messages " :status=" getStatus(status) " />
            </div>

            <!-- Chat Prompt - Fixed at Bottom -->
            <div class="flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <UChatPrompt v-model=" input " @submit=" handleSubmit ">
                    
                    <template #header>
                            <!-- Character count -->
                            <div v-if=" input.length > 0 " class="text-xs text-gray-500" :class=" {
                                'text-amber-600': input.length > 3200,
                                'text-red-600': input.length > 4000,
                            } ">
                                {{ input.length }}/4000
                            </div>
                        </template>

                        <template #footer>
                            <div class="text-xs text-gray-500">
                                Press Enter to send, Shift+Enter for new line
                            </div>

                            <UChatPromptSubmit :status=" isLoading ? 'streaming' : 'ready' " @stop=" handleStop " />
                        </template>
                </UChatPrompt>
            </div>
        </div>
    </AppLayout>
</template>