<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class StreamingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        Cache::flush();
    }

    public function test_chat_stream_returns_proper_headers(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'messages' => [['role' => 'user', 'content' => 'Help me create a prompt for writing']]
            ]);

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/plain; charset=utf-8');
        $response->assertHeader('Cache-Control', 'no-cache, private');
        $response->assertHeader('Connection', 'keep-alive');
        $response->assertHeader('X-Vercel-AI-Data-Stream', 'v1');
    }

    public function test_chat_stream_handles_validation_errors(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'messages' => [] // Empty messages array should fail validation
            ]);

        $response->assertStatus(422);
        $response->assertHeader('Content-Type', 'application/json');
    }

    public function test_chat_stream_handles_successful_streaming(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'messages' => [['role' => 'user', 'content' => 'Hello, how are you?']]
            ]);

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/plain; charset=utf-8');
        $response->assertHeader('X-Vercel-AI-Data-Stream', 'v1');
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
